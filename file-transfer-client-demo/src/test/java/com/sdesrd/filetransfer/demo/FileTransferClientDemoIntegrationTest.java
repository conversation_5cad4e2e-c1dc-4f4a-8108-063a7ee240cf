package com.sdesrd.filetransfer.demo;

import static org.junit.Assert.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sdesrd.filetransfer.client.FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.FileInfo;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.listener.TransferListener;

/**
 * 文件传输客户端演示集成测试
 * 
 * 这个测试类将演示功能转换为可测试的集成测试用例，确保测试结果能够被正确统计和报告。
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-19
 */
public class FileTransferClientDemoIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(FileTransferClientDemoIntegrationTest.class);

    /** 测试服务器配置常量 */
    private static final String TEST_SERVER_HOST = getSystemProperty("test.server.host", "localhost");
    private static final int TEST_SERVER_PORT = Integer.parseInt(getSystemProperty("test.server.port", "49011"));
    private static final String TEST_USER_NAME = getSystemProperty("test.user.name", "demo");
    private static final String TEST_USER_SECRET = getSystemProperty("test.user.secret", "demo123");
    
    /** 测试文件配置常量 */
    private static final String TEST_UPLOAD_DIR = "test-files/upload";
    private static final String TEST_DOWNLOAD_DIR = "test-files/download";
    private static final int SMALL_FILE_SIZE = 1024 * 20;  // 20KB
    private static final int MEDIUM_FILE_SIZE = 1024 * 1024 * 2;  // 2MB
    private static final int LARGE_FILE_SIZE = 1024 * 1024 * 10;  // 10MB
    
    /** 测试超时配置常量 */
    private static final int TEST_TIMEOUT_SECONDS = 30;
    private static final int UPLOAD_TIMEOUT_SECONDS = 60;
    private static final int DOWNLOAD_TIMEOUT_SECONDS = 60;
    
    /** 客户端实例 */
    private FileTransferClient client;
    
    /** 传输监听器 */
    private TransferListener testListener;
    
    /** 测试统计 */
    private DemoStatistics statistics;

    /**
     * 获取系统属性值
     * 
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值
     */
    private static String getSystemProperty(String key, String defaultValue) {
        return System.getProperty(key, defaultValue);
    }

    /**
     * 测试前置设置
     */
    @Before
    public void setUp() throws Exception {
        log.info("[INTEGRATION_TEST] 初始化集成测试环境...");
        
        // 创建测试目录
        createTestDirectories();
        
        // 初始化客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr(TEST_SERVER_HOST)
                .serverPort(TEST_SERVER_PORT)
                .auth(TEST_USER_NAME, TEST_USER_SECRET)
                .chunkSize(1024 * 1024)  // 1MB分块大小
                .maxConcurrentTransfers(2)  // 降低并发数以避免数据库锁定
                .retry(3, 500)
                .build();

        // 设置超时时间
        config.setConnectTimeoutSeconds(TEST_TIMEOUT_SECONDS);
        config.setReadTimeoutSeconds(TEST_TIMEOUT_SECONDS);
        config.setWriteTimeoutSeconds(TEST_TIMEOUT_SECONDS);
        
        // 创建客户端实例
        client = new FileTransferClient(config);
        
        // 创建测试监听器
        testListener = new DemoTransferListener(false);  // 关闭详细日志以减少测试输出
        
        // 初始化统计
        statistics = new DemoStatistics();
        
        log.info("[INTEGRATION_TEST] 集成测试环境初始化完成");
    }

    /**
     * 测试后置清理
     */
    @After
    public void tearDown() throws Exception {
        log.info("[INTEGRATION_TEST] 清理集成测试环境...");
        
        // 关闭客户端
        if (client != null) {
            client.close();
        }
        
        // 清理测试文件
        cleanupTestFiles();
        
        log.info("[INTEGRATION_TEST] 集成测试环境清理完成");
    }

    /**
     * 测试文件上传功能
     */
    @Test
    public void testFileUpload() throws Exception {
        log.info("[INTEGRATION_TEST] 开始文件上传测试...");

        // 创建测试文件
        File testFile = createTestFile("upload-test.txt", SMALL_FILE_SIZE);
        assertTrue("测试文件应该存在", testFile.exists());

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行上传
        CompletableFuture<UploadResult> uploadFuture = client.uploadFile(
                testFile.getAbsolutePath(), null, testListener);

        UploadResult result = uploadFuture.get(UPLOAD_TIMEOUT_SECONDS, TimeUnit.SECONDS);

        // 计算耗时
        long duration = System.currentTimeMillis() - startTime;

        // 验证结果
        assertNotNull("上传结果不应为空", result);
        assertTrue("上传应该成功", result.isSuccess());
        assertNotNull("文件ID不应为空", result.getFileId());
        assertFalse("文件ID不应为空字符串", result.getFileId().trim().isEmpty());

        statistics.recordUploadSuccess(testFile.length(), duration);

        log.info("[INTEGRATION_TEST] 文件上传测试完成 - 文件ID: {}, 耗时: {}ms", result.getFileId(), duration);
    }

    /**
     * 测试文件下载功能
     */
    @Test
    public void testFileDownload() throws Exception {
        log.info("[INTEGRATION_TEST] 开始文件下载测试...");

        // 先上传一个文件
        File uploadFile = createTestFile("download-test.txt", MEDIUM_FILE_SIZE);
        UploadResult uploadResult = client.uploadFileSync(uploadFile.getAbsolutePath(), null, testListener);
        assertTrue("上传应该成功", uploadResult.isSuccess());

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行下载
        String downloadPath = TEST_DOWNLOAD_DIR + "/downloaded-" + uploadFile.getName();
        CompletableFuture<DownloadResult> downloadFuture = client.downloadFile(
                uploadResult.getFileId(), downloadPath, testListener);

        DownloadResult result = downloadFuture.get(DOWNLOAD_TIMEOUT_SECONDS, TimeUnit.SECONDS);

        // 计算耗时
        long duration = System.currentTimeMillis() - startTime;

        // 验证结果
        assertNotNull("下载结果不应为空", result);
        assertTrue("下载应该成功", result.isSuccess());

        File downloadedFile = new File(downloadPath);
        assertTrue("下载的文件应该存在", downloadedFile.exists());
        assertEquals("下载文件大小应该与原文件相同", (long)uploadFile.length(), (long)downloadedFile.length());

        statistics.recordDownloadSuccess(downloadedFile.length(), duration);

        log.info("[INTEGRATION_TEST] 文件下载测试完成 - 下载路径: {}, 耗时: {}ms", downloadPath, duration);
    }

    /**
     * 测试文件信息查询功能
     */
    @Test
    public void testFileInfoQuery() throws Exception {
        log.info("[INTEGRATION_TEST] 开始文件信息查询测试...");
        
        // 先上传一个文件
        File testFile = createTestFile("info-test.txt", SMALL_FILE_SIZE);
        UploadResult uploadResult = client.uploadFileSync(testFile.getAbsolutePath(), null, testListener);
        assertTrue("上传应该成功", uploadResult.isSuccess());
        
        // 查询文件信息
        FileInfo fileInfo = client.getFileInfo(uploadResult.getFileId());
        
        // 验证结果
        assertNotNull("文件信息不应为空", fileInfo);
        assertEquals("文件ID应该匹配", uploadResult.getFileId(), fileInfo.getFileId());
        assertEquals("文件名应该匹配", testFile.getName(), fileInfo.getFileName());
        assertEquals("文件大小应该匹配", (long)testFile.length(), (long)fileInfo.getFileSize());
        
        statistics.recordInfoQuerySuccess();
        
        log.info("[INTEGRATION_TEST] 文件信息查询测试完成 - 文件信息: {}", fileInfo);
    }

    /**
     * 测试错误处理功能
     */
    @Test
    public void testErrorHandling() throws Exception {
        log.info("[INTEGRATION_TEST] 开始错误处理测试...");
        
        // 测试无效文件ID下载
        try {
            String invalidFileId = "invalid-file-id-test";
            client.downloadFileSync(invalidFileId, TEST_DOWNLOAD_DIR + "/invalid.txt", testListener);
            fail("应该抛出异常");
        } catch (Exception e) {
            log.info("[EXPECTED_EXCEPTION_TEST] 正确处理无效文件ID错误: {}", e.getMessage());
            statistics.recordErrorHandlingTest();
        }
        
        // 测试不存在文件上传
        try {
            client.uploadFileSync("/non/existent/file.txt", null, testListener);
            fail("应该抛出异常");
        } catch (Exception e) {
            log.info("[EXPECTED_EXCEPTION_TEST] 正确处理文件不存在错误: {}", e.getMessage());
            statistics.recordErrorHandlingTest();
        }
        
        log.info("[INTEGRATION_TEST] 错误处理测试完成");
    }

    /**
     * 创建测试目录
     */
    private void createTestDirectories() throws IOException {
        Files.createDirectories(Paths.get(TEST_UPLOAD_DIR));
        Files.createDirectories(Paths.get(TEST_DOWNLOAD_DIR));
        log.info("[INTEGRATION_TEST] 创建测试目录: {} 和 {}", TEST_UPLOAD_DIR, TEST_DOWNLOAD_DIR);
    }

    /**
     * 创建测试文件
     * 
     * @param fileName 文件名
     * @param size 文件大小
     * @return 创建的文件
     */
    private File createTestFile(String fileName, int size) throws IOException {
        File file = new File(TEST_UPLOAD_DIR, fileName);
        byte[] data = new byte[size];
        // 填充测试数据
        for (int i = 0; i < size; i++) {
            data[i] = (byte) (i % 256);
        }
        Files.write(file.toPath(), data);
        log.info("[INTEGRATION_TEST] 创建测试文件: {} ({}字节)", file.getAbsolutePath(), size);
        return file;
    }

    /**
     * 清理测试文件
     */
    private void cleanupTestFiles() {
        try {
            deleteDirectory(new File(TEST_UPLOAD_DIR));
            deleteDirectory(new File(TEST_DOWNLOAD_DIR));
            log.info("[INTEGRATION_TEST] 清理测试文件完成");
        } catch (Exception e) {
            log.warn("[INTEGRATION_TEST] 清理测试文件时发生错误: {}", e.getMessage());
        }
    }

    /**
     * 递归删除目录
     * 
     * @param directory 要删除的目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
